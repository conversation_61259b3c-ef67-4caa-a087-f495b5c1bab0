import type { CodeKeywordDefinition, ErrorObject, AnySchema } from "../../types";
import type { KeywordCxt } from "../../compile/validate";
export declare type AdditionalItemsError = ErrorObject<"additionalItems", {
    limit: number;
}, AnySchema>;
declare const def: CodeKeywordDefinition;
export declare function validateAdditionalItems(cxt: KeywordCxt, items: AnySchema[]): void;
export default def;
