<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:background="#44000000"
  android:id="@+id/main_layout"
  >

  <FrameLayout
    android:id="@+id/bottom_sheet"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:elevation="4dp"
    app:behavior_expandedOffset="40dp"
    app:behavior_fitToContents="false"
    app:behavior_halfExpandedRatio="0.6"
    app:behavior_hideable="true"
    app:behavior_peekHeight="0dp"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

  </FrameLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>