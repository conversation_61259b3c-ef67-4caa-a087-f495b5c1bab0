{"name": "localone-vault", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "expo": "~53.0.17", "expo-camera": "^16.1.11", "expo-file-system": "^18.1.11", "expo-image-picker": "^16.1.4", "expo-media-library": "^17.1.7", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.0", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}