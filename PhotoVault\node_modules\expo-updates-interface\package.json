{"name": "expo-updates-interface", "version": "1.1.0", "description": "Native interface for modules that optionally depend on expo-updates, e.g. expo-dev-launcher.", "main": "index.js", "keywords": ["react-native", "expo", "expo-updates-interface"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-updates-interface"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev", "dependencies": {}, "peerDependencies": {"expo": "*"}, "gitHead": "68b8233002dc678934ba40cbade7fbc80e71aeff"}